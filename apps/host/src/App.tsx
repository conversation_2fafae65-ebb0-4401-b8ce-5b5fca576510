import React, { useLayoutEffect, useMemo } from 'react';
import {
  LogBox,
  Platform,
  StyleProp,
  UIManager,
  ViewStyle,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useAuth } from '@btaskee/auth-store';
import {
  ConfigHelpers,
  Log,
  navigationRef,
  QueryClient,
  QueryProvider,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { NavigationContainer } from '@react-navigation/native';

import { RootNavigator } from '@navigation/RootNavigator';

const App = () => {
  const containerStyle = useMemo<StyleProp<ViewStyle>>(() => ({ flex: 1 }), []);
  const queryClient = new QueryClient();
  const { onChangeIsoCode } = useAppStore();
  const { setUser } = useUserStore();
  const { login } = useAuth();

  useLayoutEffect(() => {
    // Enable layout animations for Android
    // This is required for LayoutAnimation.configureNext() to work on Android
    if (
      Platform.OS === 'android' &&
      UIManager.setLayoutAnimationEnabledExperimental
    ) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }

    if (__DEV__) {
      //Khi ở môi trường dev và được mở bằng detox có arguments isE2ETesting thì cache vào static biến isE2ETesting
      const { LaunchArguments } = require('react-native-launch-arguments');
      const isE2ETesting = Boolean(LaunchArguments.value()?.isE2ETesting);
      const initialRouteName = LaunchArguments.value()?.initialRouteName;
      const isoCode = LaunchArguments.value()?.isoCode;
      const requireUser = LaunchArguments.value()?.requireUser;

      ConfigHelpers.setIsE2ETesting(isE2ETesting);
      ConfigHelpers.setInitialRouteName(initialRouteName);
      ConfigHelpers.setIsoCode(isoCode);
      ConfigHelpers.setRequireUser(requireUser);
      if (isoCode) {
        onChangeIsoCode(isoCode);
      }
      if (requireUser) {
        setUser(requireUser);
        login(
          requireUser?.services?.loginTokens,
          requireUser?.services?.loginTokens,
          requireUser._id,
        );
        Log.consoleLog(
          `============== set user = ${requireUser?.phone} ==============`,
        );
      }
      Log.consoleLog(`============== isoCode = ${isoCode} ==============`);
      Log.consoleLog(
        `============== isE2ETesting = ${isE2ETesting} ==============`,
      );
      Log.consoleLog(
        `============== initialRouteName = ${initialRouteName} ==============`,
      );
      if (isE2ETesting) {
        LogBox.ignoreAllLogs();
      }
    }
  }, []);

  return (
    <SafeAreaProvider>
      <QueryProvider client={queryClient}>
        <GestureHandlerRootView style={containerStyle}>
          <KeyboardProvider>
            <NavigationContainer ref={navigationRef as any}>
              <RootNavigator />
            </NavigationContainer>
          </KeyboardProvider>
        </GestureHandlerRootView>
      </QueryProvider>
    </SafeAreaProvider>
  );
};

export default App;
