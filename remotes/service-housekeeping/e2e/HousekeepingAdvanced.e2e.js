/**
 * Housekeeping Service Advanced E2E Tests
 *
 * Comprehensive E2E test suite for React Native housekeeping service booking application
 * following strict sequential flows: Address Selection → Service Configuration → Date/Time Selection → Payment/Confirmation
 *
 * Requirements:
 * - TestID-only selectors (absolutely no text expectations or accessibility labels)
 * - Sequential flow compliance (cannot skip steps)
 * - Incremental scrolling patterns (~150 pixels per scroll) for scroll-to-reveal viewport management
 * - Performance optimized targeting 3-5 minute execution time
 * - Enhanced testID coverage for all interactive elements
 * - Comprehensive validation for both new address creation and existing address selection flows
 * - Single country per test suite (VN only)
 * - Zustand store state management validation
 *
 * Flow 1 (New Address Creation):
 * 1. Create new hostel address
 * 2. Choose hostel type (Hotel/Homestay, Serviced apartment, Villa/House)
 * 3. Choose room configuration (Single, Double, Family, Dormitory)
 * 4. Optionally select lobby cleaning (+45 minutes)
 * 5. Choose date/time
 * 6. Navigate to confirmation and payment page
 *
 * Flow 2 (Existing Address Selection):
 * 1. Select existing hostel address
 * 2. Choose hostel type (Hotel/Homestay, Serviced apartment, Villa/House)
 * 3. Choose room configuration (Single, Double, Family, Dormitory)
 * 4. Optionally select lobby cleaning (+45 minutes)
 * 5. Choose date/time
 * 6. Navigate to confirmation and payment page
 *
 * <AUTHOR> Automation Engineer
 * @framework Detox
 * @version 4.0 - Advanced comprehensive flow validation with performance optimization
 */

const { device } = require('detox');
const {
  initData,
  tapId,
  waitForElement,
  expectElementVisible,
  typeToTextField,
  removeIntroHouseKeeping,
  tapIdService,
  scroll,
  swipe,
  clearTextInput,
} = require('./step-definition');

// Performance monitoring utilities with enhanced tracking
const performanceTracker = {
  startTime: null,
  stepTimes: {},
  flowMetrics: {},

  startTest: function (testName) {
    this.startTime = Date.now();
    this.stepTimes[testName] = { start: this.startTime };
    console.log(`🚀 Starting test: ${testName}`);
  },

  endTest: function (testName) {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    this.stepTimes[testName].end = endTime;
    this.stepTimes[testName].duration = duration;

    // Enhanced performance tracking
    if (duration > 300000) {
      this.stepTimes[testName].warning = 'Exceeds 5 minute target';
      console.warn(`⚠️ Performance Warning: ${testName} took ${duration}ms`);
    } else {
      console.log(`✅ Performance OK: ${testName} completed in ${duration}ms`);
    }

    return duration;
  },

  logStepTime: function (stepName) {
    const stepTime = Date.now() - this.startTime;
    this.flowMetrics[stepName] = stepTime;
    console.log(`📊 Step "${stepName}" completed at ${stepTime}ms`);
  },

  getResults: function () {
    return {
      stepTimes: this.stepTimes,
      flowMetrics: this.flowMetrics,
    };
  },
};

// Optimized wait times for better performance
const WAIT_TIMES = {
  FAST: 2000, // For quick transitions
  NORMAL: 5000, // For standard navigation
  SLOW: 8000, // For complex operations
  EXTENDED: 10000, // For heavy operations
};

// Precise incremental scrolling helper to avoid overshooting elements
const scrollPrecise = async (containerId, direction = 'down', pixels = 150) => {
  // Use precise pixel-based scrolling instead of aggressive swipe
  // Default 150 pixels provides controlled movement without overshooting
  await scroll(containerId, pixels, direction, 0.5, 0.5);
  // Small delay to allow UI to settle
  await new Promise((resolve) => setTimeout(resolve, 200));
};

// Enhanced scroll-to-reveal element helper with incremental scrolling
const scrollToRevealElement = async (
  containerId,
  targetElementId,
  direction = 'down',
  maxAttempts = 10,
  scrollIncrement = 150,
) => {
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      // Check if target element is visible
      await waitForElement(targetElementId, 1000);
      console.log(
        `✅ Element ${targetElementId} found after ${attempts} scroll attempts`,
      );
      return true;
    } catch (error) {
      // Element not visible, scroll incrementally
      await scroll(containerId, scrollIncrement, direction, 0.5, 0.5);
      attempts++;

      // Small delay to allow UI to settle
      await new Promise((resolve) => setTimeout(resolve, 200));

      console.log(
        `🔄 Scroll attempt ${attempts}/${maxAttempts} for ${targetElementId}`,
      );
    }
  }

  throw new Error(
    `❌ Failed to reveal element ${targetElementId} after ${maxAttempts} scroll attempts`,
  );
};

// Enhanced error handling wrapper
const safeExecute = async (operation, operationName, retries = 2) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await operation();
      console.log(`✅ ${operationName} succeeded on attempt ${attempt}`);
      return;
    } catch (error) {
      console.warn(
        `⚠️ ${operationName} failed on attempt ${attempt}: ${error.message}`,
      );
      if (attempt === retries) {
        throw new Error(
          `❌ ${operationName} failed after ${retries} attempts: ${error.message}`,
        );
      }
      // Wait before retry
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }
};

// Test data for VN country only (single country per test suite)
const ASKER_VN = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const TEST_LOCATION_VN = {
  _id: 'xceca5d1c28bed6ca7a42acf82e5951e7',
  address:
    'Place in Saigon apartment 2, Phường 22, Bình Thạnh, Hồ Chí Minh, Việt Nam',
  city: 'Hồ Chí Minh',
  contact: 'ba test',
  country: 'VN',
  countryCode: '+84',
  description: '222',
  district: 'Bình Thạnh',
  isoCode: 'VN',
  lat: 10.7906155,
  lng: 106.7173618,
  locationName: 'nhà 35',
  phoneNumber: '0777777777',
  shortAddress: 'Vietnam Bình Thạnh',
};

// Hostel type configurations for testing
const HOSTEL_TYPES = {
  HOTEL: 'HOTEL',
  APARTMENT: 'APARTMENT',
  VILLA: 'VILLA',
};

// Room type configurations for testing
const ROOM_TYPES = {
  SINGLE: 'SINGLE',
  DOUBLE: 'DOUBLE',
  FAMILY: 'FAMILY',
  DORMITORY: 'DORMITORY',
};

describe('Housekeeping Service Advanced Sequential Flow', () => {
  beforeEach(async () => {
    // Performance optimized app reset with enhanced data initialization
    performanceTracker.startTest('beforeEach-setup');

    // Reset app state and data for consistent testing
    await safeExecute(async () => {
      await initData('resetData');
    }, 'Reset Data');

    await safeExecute(async () => {
      await initData('user/createUser', [ASKER_VN]);
    }, 'Create User');

    await safeExecute(async () => {
      await initData('user/updateUser', {
        phone: ASKER_VN.phone,
        isoCode: ASKER_VN.isoCode,
        dataUpdate: {
          housekeepingLocations: [TEST_LOCATION_VN],
        },
      });
    }, 'Update User with Location');

    try {
      // Navigate to housekeeping service
      await tapIdService('postTaskServiceHOUSE_KEEPING');
      await removeIntroHouseKeeping();

      // Use optimized wait time for faster execution
      await waitForElement('scrollChooseAddress', WAIT_TIMES.NORMAL);
      performanceTracker.logStepTime('Service Navigation');
    } catch (error) {
      // Handle any intro screens if present
      console.warn('Intro handling required, using extended wait time');
      await waitForElement('scrollChooseAddress', WAIT_TIMES.EXTENDED);
      performanceTracker.logStepTime('Service Navigation (Extended)');
    }

    performanceTracker.endTest('beforeEach-setup');
  });

  describe('Comprehensive End-to-End Flows', () => {
    it.only('should complete comprehensive new address creation booking flow successfully', async () => {
      performanceTracker.startTest('new-address-creation-comprehensive-flow');

      // Step 1: Create new hostel address - Navigate to address selection
      await safeExecute(async () => {
        await expectElementVisible('scrollChooseAddress');
        await scrollToRevealElement(
          'scrollChooseAddress',
          'btnAddNewLocation',
          'down',
          5,
          150,
        );
        await tapId('btnAddNewLocation');
      }, 'Step 1: Navigate to Address Selection');

      performanceTracker.logStepTime('Address Selection Navigation');

      // Step 1a: Select an address from ChooseAddress screen
      await safeExecute(async () => {
        await waitForElement('scrollChooseAddress', WAIT_TIMES.NORMAL);
        await scrollToRevealElement(
          'scrollChooseAddress',
          'address1',
          'down',
          5,
          150,
        );
        await tapId('address1');
      }, 'Step 1a: Select Address');

      performanceTracker.logStepTime('Address Selection');

      // Step 1b: Fill in CreateNewHostel form
      await safeExecute(async () => {
        await waitForElement('txtLocationName', WAIT_TIMES.NORMAL);
        await clearTextInput('txtLocationName');
        await typeToTextField('txtLocationName', 'Test Hostel Location');
        
        await typeToTextField('txtContactName', 'Test Contact Name');
        await typeToTextField('txtPhoneNumber', '0123456789');
      }, 'Step 1b: Fill Hostel Details');

      // Step 1c: Save the new location
      await safeExecute(async () => {
        await scrollToRevealElement(
          'txtPhoneNumber',
          'btnSaveLocation',
          'down',
          5,
          150,
        );
        await tapId('btnSaveLocation');
      }, 'Step 1c: Save New Location');

      performanceTracker.logStepTime('Location Creation Complete');

      // Step 1d: Back to ChooseHostel, select the newly created location
      await safeExecute(async () => {
        await waitForElement('scrollChooseAddress', WAIT_TIMES.NORMAL);
        await scrollToRevealElement(
          'scrollChooseAddress',
          'address1',
          'down',
          5,
          150,
        );
        await tapId('address1');
      }, 'Step 1d: Select Created Location');

      // Verify navigation to hostel type selection
      await safeExecute(async () => {
        await waitForElement(
          'home-type-selection-container',
          WAIT_TIMES.NORMAL,
        );
        await expectElementVisible('home-type-selection-container');
      }, 'Step 1: Verify Hostel Type Screen');

      // Step 2: Choose hostel type (Hotel/Homestay)
      await safeExecute(async () => {
        await scrollToRevealElement(
          'home-type-selection-container',
          'btnHostelType_HOTEL',
          'down',
          5,
          150,
        );
        await tapId('btnHostelType_HOTEL');
      }, 'Step 2: Select Hotel Type');

      performanceTracker.logStepTime('Hostel Type Selection');

      // Verify navigation to room configuration
      await safeExecute(async () => {
        await waitForElement(
          'room-type-selection-container',
          WAIT_TIMES.NORMAL,
        );
        await expectElementVisible('room-type-selection-container');
      }, 'Step 2: Verify Room Configuration Screen');

      // Step 3: Choose room configuration (Single room)
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'btnIncrement_SINGLE',
          'down',
          5,
          150,
        );
        await tapId('btnIncrement_SINGLE');
      }, 'Step 3: Select Single Room');

      // Add room number
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'room-number-input',
          'down',
          6,
          150,
        );
        await typeToTextField('room-number-input', '301');
      }, 'Step 3: Add Room Number');

      performanceTracker.logStepTime('Room Configuration');

      // Step 4: Optionally select lobby cleaning (+45 minutes)
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'working-process-button',
          'down',
          6,
          150,
        );
        await expectElementVisible('working-process-button');
      }, 'Step 4: Verify Lobby Cleaning Option');

      // Navigate to date/time selection
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'btnNextStep3',
          'down',
          6,
          150,
        );
        await tapId('btnNextStep3');
      }, 'Step 4: Navigate to Date/Time');

      performanceTracker.logStepTime('Service Configuration Complete');

      // Step 5: Choose date/time
      await safeExecute(async () => {
        await waitForElement('date-picker-component', WAIT_TIMES.NORMAL);
        await expectElementVisible('date-picker-component');
        await expectElementVisible('time-picker-component');
      }, 'Step 5: Verify Date/Time Components');

      // Add booking note
      await safeExecute(async () => {
        await scrollToRevealElement(
          'note-input-component',
          'note-input-component',
          'down',
          5,
          150,
        );
        await typeToTextField(
          'note-input-component',
          'Comprehensive new address flow test - advanced validation',
        );
      }, 'Step 5: Add Booking Note');

      // Navigate to confirmation
      await safeExecute(async () => {
        await scrollToRevealElement(
          'note-input-component',
          'btnNextStep3',
          'down',
          5,
          150,
        );
        await tapId('btnNextStep3');
      }, 'Step 5: Navigate to Confirmation');

      performanceTracker.logStepTime('Date/Time Selection Complete');

      // Step 6: Navigate to confirmation and payment page
      await safeExecute(async () => {
        await waitForElement('scrollViewStep4', WAIT_TIMES.NORMAL);
        await expectElementVisible('scrollViewStep4');
      }, 'Step 6: Verify Confirmation Screen');

      // Verify all confirmation sections
      await safeExecute(async () => {
        await scrollToRevealElement(
          'scrollViewStep4',
          'location-post-task-container',
          'down',
          5,
          150,
        );
        await expectElementVisible('location-post-task-container');
      }, 'Step 6: Verify Location Details');

      await safeExecute(async () => {
        await scrollToRevealElement(
          'scrollViewStep4',
          'task-detail-container',
          'down',
          5,
          150,
        );
        await expectElementVisible('task-detail-container');
      }, 'Step 6: Verify Task Details');

      await safeExecute(async () => {
        await scrollToRevealElement(
          'scrollViewStep4',
          'btnSubmitPostTask',
          'down',
          8,
          150,
        );
        await expectElementVisible('btnSubmitPostTask');
      }, 'Step 6: Verify Submit Button');

      performanceTracker.logStepTime('Confirmation Screen Complete');

      // New address creation booking flow completed successfully
      const duration = performanceTracker.endTest(
        'new-address-creation-comprehensive-flow',
      );
      expect(duration).toBeLessThan(300000); // Should complete within 5 minutes

      console.log(
        '🎯 New Address Creation Flow Performance Results:',
        performanceTracker.getResults(),
      );
    });

    it('should complete comprehensive existing address selection booking flow successfully', async () => {
      performanceTracker.startTest(
        'existing-address-selection-comprehensive-flow',
      );

      // Step 1: Select existing hostel address
      await safeExecute(async () => {
        await expectElementVisible('scrollChooseAddress');
        await scrollToRevealElement(
          'scrollChooseAddress',
          'address1',
          'down',
          5,
          150,
        );
        await tapId('address1');
      }, 'Step 1: Select Existing Address');

      performanceTracker.logStepTime('Existing Address Selection');

      // Verify navigation to service configuration
      await safeExecute(async () => {
        await waitForElement(
          'home-type-selection-container',
          WAIT_TIMES.NORMAL,
        );
        await expectElementVisible('home-type-selection-container');
      }, 'Step 1: Verify Service Configuration Screen');

      // Step 2: Choose hostel type (Hotel/Homestay)
      await safeExecute(async () => {
        await scrollToRevealElement(
          'home-type-selection-container',
          'btnHostelType_HOTEL',
          'down',
          5,
          150,
        );
        await tapId('btnHostelType_HOTEL');
      }, 'Step 2: Select Hotel Type');

      performanceTracker.logStepTime('Hostel Type Selection');

      // Verify navigation to room configuration
      await safeExecute(async () => {
        await waitForElement(
          'room-type-selection-container',
          WAIT_TIMES.NORMAL,
        );
        await expectElementVisible('room-type-selection-container');
      }, 'Step 2: Verify Room Configuration Screen');

      // Step 3: Choose room configuration (Single room)
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'btnIncrement_SINGLE',
          'down',
          5,
          150,
        );
        await tapId('btnIncrement_SINGLE');
      }, 'Step 3: Select Single Room');

      // Add room number
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'room-number-input',
          'down',
          6,
          150,
        );
        await typeToTextField('room-number-input', '205');
      }, 'Step 3: Add Room Number');

      performanceTracker.logStepTime('Room Configuration');

      // Step 4: Optionally select lobby cleaning (+45 minutes)
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'working-process-button',
          'down',
          6,
          150,
        );
        await expectElementVisible('working-process-button');
      }, 'Step 4: Verify Lobby Cleaning Option');

      // Navigate to date/time selection
      await safeExecute(async () => {
        await scrollToRevealElement(
          'room-type-selection-container',
          'btnNextStep3',
          'down',
          6,
          150,
        );
        await tapId('btnNextStep3');
      }, 'Step 4: Navigate to Date/Time');

      performanceTracker.logStepTime('Service Configuration Complete');

      // Step 5: Choose date/time
      await safeExecute(async () => {
        await waitForElement('date-picker-component', WAIT_TIMES.NORMAL);
        await expectElementVisible('date-picker-component');
        await expectElementVisible('time-picker-component');
      }, 'Step 5: Verify Date/Time Components');

      // Add booking note
      await safeExecute(async () => {
        await scrollToRevealElement(
          'note-input-component',
          'note-input-component',
          'down',
          5,
          150,
        );
        await typeToTextField(
          'note-input-component',
          'Comprehensive existing address flow test - advanced validation',
        );
      }, 'Step 5: Add Booking Note');

      // Navigate to confirmation
      await safeExecute(async () => {
        await scrollToRevealElement(
          'note-input-component',
          'btnNextStep3',
          'down',
          5,
          150,
        );
        await tapId('btnNextStep3');
      }, 'Step 5: Navigate to Confirmation');

      performanceTracker.logStepTime('Date/Time Selection Complete');

      // Step 6: Navigate to confirmation and payment page
      await safeExecute(async () => {
        await waitForElement('scrollViewStep4', WAIT_TIMES.NORMAL);
        await expectElementVisible('scrollViewStep4');
      }, 'Step 6: Verify Confirmation Screen');

      // Verify all confirmation sections
      await safeExecute(async () => {
        await scrollToRevealElement(
          'scrollViewStep4',
          'location-post-task-container',
          'down',
          5,
          150,
        );
        await expectElementVisible('location-post-task-container');
      }, 'Step 6: Verify Location Details');

      await safeExecute(async () => {
        await scrollToRevealElement(
          'scrollViewStep4',
          'task-detail-container',
          'down',
          5,
          150,
        );
        await expectElementVisible('task-detail-container');
      }, 'Step 6: Verify Task Details');

      await safeExecute(async () => {
        await scrollToRevealElement(
          'scrollViewStep4',
          'btnSubmitPostTask',
          'down',
          8,
          150,
        );
        await expectElementVisible('btnSubmitPostTask');
      }, 'Step 6: Verify Submit Button');

      performanceTracker.logStepTime('Confirmation Screen Complete');

      // Existing address selection booking flow completed successfully
      const duration = performanceTracker.endTest(
        'existing-address-selection-comprehensive-flow',
      );
      expect(duration).toBeLessThan(300000); // Should complete within 5 minutes

      console.log(
        '🎯 Existing Address Selection Flow Performance Results:',
        performanceTracker.getResults(),
      );
    });
  });

  describe('Performance Optimization and Error Handling', () => {
    it('should validate performance optimization across both flows', async () => {
      performanceTracker.startTest('performance-optimization-validation');

      const startTime = Date.now();

      // Test rapid execution of existing address flow with performance tracking
      await safeExecute(async () => {
        await scrollPrecise('scrollChooseAddress', 'down', 100);
        await tapId('address1');
        await waitForElement('home-type-selection-container', WAIT_TIMES.FAST);
      }, 'Rapid Address Selection');

      await safeExecute(async () => {
        await scrollPrecise('home-type-selection-container', 'down', 100);
        await tapId('btnHostelType_HOTEL');
        await waitForElement('room-type-selection-container', WAIT_TIMES.FAST);
      }, 'Rapid Hostel Type Selection');

      await safeExecute(async () => {
        await scrollPrecise('room-type-selection-container', 'down', 150);
        await tapId('btnIncrement_SINGLE');
        await scrollPrecise('room-type-selection-container', 'down', 150);
        await tapId('btnNextStep3');
        await waitForElement('date-picker-component', WAIT_TIMES.FAST);
      }, 'Rapid Room Configuration');

      await safeExecute(async () => {
        await scrollPrecise('note-input-component', 'down', 150);
        await tapId('btnNextStep3');
        await waitForElement('scrollViewStep4', WAIT_TIMES.FAST);
      }, 'Rapid Date/Time and Confirmation');

      const executionTime = Date.now() - startTime;
      const maxTime = 3 * 60 * 1000; // 3 minutes

      expect(executionTime).toBeLessThan(maxTime);
      console.log(
        `🚀 Performance validation: flow completed in ${executionTime}ms (target: <${maxTime}ms)`,
      );

      performanceTracker.endTest('performance-optimization-validation');
    });

    it('should handle navigation errors and sequential flow enforcement', async () => {
      performanceTracker.startTest('error-handling-navigation');

      // Verify sequential flow enforcement
      await safeExecute(async () => {
        await expectElementVisible('scrollChooseAddress');
      }, 'Verify Initial State');

      // Test back navigation works correctly
      await safeExecute(async () => {
        await tapId('btnAddNewLocation');
        await waitForElement(
          'home-type-selection-container',
          WAIT_TIMES.NORMAL,
        );

        // Simulate back navigation (if available)
        try {
          await tapId('header-back');
          await expectElementVisible('scrollChooseAddress');
          console.log('✅ Back navigation working correctly');
        } catch (error) {
          console.log(
            'ℹ️ Back navigation not available or handled differently',
          );
        }
      }, 'Test Back Navigation');

      performanceTracker.endTest('error-handling-navigation');
    });
  });
});
